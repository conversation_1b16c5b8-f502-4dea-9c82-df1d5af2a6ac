#SpicyLyricsPage .LyricsContainer {
  height: 100%;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  overflow: hidden ;
  width: 100%;
}

#SpicyLyricsPage .LyricsContainer.Hidden {
  display: none;
}

#SpicyLyricsPage .LyricsContainer .LyricsContent {
  --TextGlowDef: rgba(255,255,255,0.15) 0px 0px 6px;
  --ActiveTextGlowDef: rgba(255,255,255,0.25) 0px 0px 16px;
  --StrongTextGlowDef: rgba(255,255,255,0.68) 0px 0px 16px;
  --StrongerTextGlowDef: rgba(255,255,255,0.74) 0px 0px 16px;
  --DefaultLyricsSize: clamp(1.5rem,calc(.425cqw * 7), 3rem);
  --DefaultLyricsSize-Small: clamp(1.1rem,calc(1cqw* 6), 1.5rem);
  --Simplebar-Scrollbar-Color: rgba(255, 255, 255, 0.6);

  overflow-x: hidden !important;
  overflow-y: auto !important;
  height: 100cqh;
  width: 100%;
  font-size: var(--DefaultLyricsSize);
  font-weight: 700;
  transition: opacity, transform, scale 0.2s linear;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  z-index: 9;

  --ImageMask: linear-gradient(
    180deg,
    transparent 0,                          /* Fully transparent at the top */
    var(--spice-sidebar) 128px,              /* Sidebar color starts at 16px */
    var(--spice-sidebar) calc(100% - 128px), /* Sidebar color ends before the bottom */
    transparent calc(100% - 8px),          /* Starts fading to transparent */
    transparent                             /* Fully transparent at the bottom */
  );

  --webkit-mask-image: var(--ImageMask);
  mask-image: var(--ImageMask);
}

#SpicyLyricsPage .LyricsContainer .LyricsContent .BottomSpacer {
  display: block;
  height: 50cqh;
}

#SpicyLyricsPage .LyricsContainer .LyricsContent .TopSpacer {
  display: block;
  height: 25cqh;
}

#SpicyLyricsPage .ContentBox .NowBar:not(.Active) + .LyricsContainer .LyricsContent .simplebar-content-wrapper {
  padding: 0 18cqw;
}

#SpicyLyricsPage .ContentBox .NowBar.Active:is(.LeftSide) + .LyricsContainer .LyricsContent .simplebar-content-wrapper .simplebar-content {
  padding: 0 5cqw 0 3.5cqw !important;
}

#SpicyLyricsPage .ContentBox .NowBar.Active:is(.RightSide) + .LyricsContainer .LyricsContent .simplebar-content-wrapper .simplebar-content {
  padding: 0 3.5cqw 0 5cqw !important;
}

/* Amai Page Button Styles */

#SpicyLyricsPage .AmaiPageButtonContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0;
  margin-top: 8px;
}

#SpicyLyricsPage .AmaiPageButton {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

#SpicyLyricsPage .AmaiPageButton:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  color: rgba(255, 255, 255, 1);
  /* transform: scale(1.025); */
}

#SpicyLyricsPage .AmaiPageButton:active {
  transform: scale(0.95);
}

#SpicyLyricsPage .AmaiPageButton.hidden {
  opacity: 0;
  pointer-events: none;
  transform: scale(0.8);
}

/* Hide refresh button when lyrics container is hidden */
#SpicyLyricsPage .ContentBox.LyricsHidden .AmaiPageButtonContainer {
  display: none;
}

/* Hide refresh button when in fullscreen mode */
#SpicyLyricsPage.Fullscreen .AmaiPageButtonContainer {
  display: none;
}

header.main-topBar-container .amai-info {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.55); /* Dark background */
  padding: 8px;
  text-align: center; /* Centered text */
  font-size: 13px;
  color: #fff; /* White text */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Slightly stronger shadow */
  text-decoration: none;
}

.simplebar-content .line {
  padding-top: 0.4rem;
  padding-bottom: 0.4rem;
}

#amai-settings button, #amai-dev-settings button, #amai-info button {
  --encore-control-size-smaller: 32px;
  --encore-spacing-tighter-4: 4px;
  --encore-spacing-base: 16px;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  border-radius: var(--encore-button-corner-radius,9999px);
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  text-transform: none;
  touch-action: manipulation;
  transition-duration: 33ms;
  transition-property: background-color, border-color, color, box-shadow, filter, transform;
  user-select: none;
  vertical-align: middle;
  transform: translate3d(0px, 0px, 0px);
  border: 1px solid var(--essential-subdued,#818181);
  color: var(--text-base,#000000);
  min-inline-size: 0px;
  min-block-size: var(--encore-control-size-smaller,32px);
  display: inline-flex;
  -webkit-box-align: center;
  align-items: center;
  -webkit-box-pack: center;
  justify-content: center;
  min-block-size: var(--encore-control-size-smaller);
  padding-block: var(--encore-spacing-tighter-4);
  padding-inline: var(--encore-spacing-base);
}

#amai-settings button:hover, #amai-dev-settings button:hover, #amai-info button:hover {
  transform: scale(1.04);
  border: 1px solid var(--essential-base,#000000);
}

#amai-settings button:active, #amai-dev-settings button:active, #amai-info button:active {
  opacity: 0.7;
  outline: none;
  transform: scale(1);
  border: 1px solid var(--essential-subdued,#818181);
}

.BoxComponent-box-elevated {
  opacity: 0.9; /* Increased opacity for better visibility */
  padding-top: 8px;
  padding-bottom: 8px;
  min-block-size: 38px;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black background */
  backdrop-filter: blur(15px); /* Blur effect for acrylic look */
  -webkit-backdrop-filter: blur(15px); /* Safari support */
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.15); /* Subtle border */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2); /* Slightly stronger shadow */
  color: white;
}

#SpicyLyricsPage .ContentBox .NowBar .Header {
  padding: 0 2cqh;
}

#SpicyLyricsPage .ContentBox .NowBar.Active + .LyricsContainer .LyricsContent .simplebar-content-wrapper .simplebar-content {
  padding: 3cqh 3cqh 1.8cqh 3cqh;
  background-color: rgba(255, 255, 255, 0.1); /* Lowered opacity for stronger acrylic effect */
  backdrop-filter: blur(20px); /* Increased blur for more acrylic */
  -webkit-backdrop-filter: blur(20px); /* Safari support */
  border: 1px solid rgba(255, 255, 255, 0.2); /* Optional: enhances the frosted look */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Optional shadow */
}

.Root__right-sidebar:has(.main-nowPlayingView-section, canvas) .main-nowPlayingView-section {
  background-color: rgba(255, 255, 255, 0.1); /* Lowered opacity for stronger acrylic effect */
  border-radius: 1cqh;
  backdrop-filter: blur(20px); /* Increased blur for more acrylic */
  -webkit-backdrop-filter: blur(20px); /* Safari support */
  border: 1px solid rgba(255, 255, 255, 0.2); /* Optional: enhances the frosted look */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Optional shadow */
}

.Root__right-sidebar:has(.main-nowPlayingView-section, canvas) .main-nowPlayingView-section > button > div > div {
  background-color: transparent; /* Lowered opacity for stronger acrylic effect */
}

#SpicyLyricsPage .ContentBox .NowBar .Header .Metadata {
  margin:1.8cqh 0 0 0;
}

ruby {
  margin-top:-0.5rem;
}

ruby.romaja {
  margin-right: 0.25rem;
}

ruby > rt {
  margin-bottom:0.15rem;
  font-size:55%;
}

.Button-buttonSecondary-small-useBrowserDefaultFocusStyle {
  border: 1px solid rgba(255, 255, 255, 0.65);
}

.amai-settings-header {
  color: var(--text-base,#ffffff);
  font-size: 1.25rem;
  font-weight: 700;
}

.translation {
  display: block;
  font-size: 1.15rem;
  /* color: #ccc; */
  line-height: 1.35rem;
  font-weight: 500;
}

.line {
  display: flex;
  flex-direction: column;
}

.main-lyrics-text {
  display: block;
  width: 100%;
}

.main-trackList-trackListRow:focus-within, .main-trackList-trackListRow:hover, .BoxComponent-group-card-naked-isInteractive-draggable:hover::after {
  background-color: rgba(255, 255, 255, 0.1); /* Lowered opacity for stronger acrylic effect */
  backdrop-filter: blur(20px); /* Increased blur for more acrylic */
  -webkit-backdrop-filter: blur(20px); /* Safari support */
  border: 1px solid rgba(255, 255, 255, 0.2); /* Optional: enhances the frosted look */
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Optional shadow */
}

.main-nowPlayingView-credits .HeaderArea {
  flex-direction: column;
  align-items: start;
}

.main-nowPlayingView-credits > *:not(:first-child)::before {
  content: "";
  display: block;
  height: .25px;
  background: rgb(255 255 255 / 25%);
  margin-bottom: .55rem;
}

/* Hide ads placeholder element*/
#main-view > div > div[data-testid=test-ref-div] {
  display: none;
}