/* 
 * Enhanced Dynamic Background
 * - Creates immersive visual experience
 * - Enhanced color blending and gradients
 * - Optimized performance with hardware acceleration
 * - Improved transition smoothness
 */

.sweet-dynamic-bg {
  /* Base variables */
  --bg-hue-shift: 0deg;
  --bg-saturation: 2.2; /* Increased for richer colors */
  --bg-brightness: 0.5; /* Slightly dimmer for better contrast */
  --bg-blur-primary: 35px; /* Increased blur for primary layer */
  --bg-blur-secondary: 55px; /* Increased blur for secondary layer */
  
  /* Rotation variables for different elements */
  --bg-rotation-primary: 0deg;
  --bg-rotation-secondary: 15deg; /* Offset rotation for depth */
  --bg-scale-primary: 1.1; /* Slightly larger scale */
  --bg-scale-secondary: 1.2; /* Slightly larger scale */
  
  height: 100%;
  left: 0;
  overflow: hidden;
  pointer-events: none;
  position: absolute;
  top: 0;
  width: 100%;
  
  /* Apply container-level filters instead of per-element */
  filter: saturate(var(--bg-saturation)) brightness(var(--bg-brightness));
  
  /* Create stacking context for better compositing */
  isolation: isolate;
  
  /* Optimize for animation */
  will-change: transform;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Add a gradient overlay for depth and color blending */
.sweet-dynamic-bg::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(15, 15, 35, 0.4) 0%, rgba(30, 10, 70, 0.3) 50%, rgba(10, 20, 50, 0.4) 100%);
  z-index: 10; /* Above all other elements in the dynamic bg */
  pointer-events: none;
  animation: gradientShift 30s linear infinite; /* Slow gradient movement */
}

/* Enhanced placeholder with color hint from current theme */
.sweet-dynamic-bg .placeholder {
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle at center,
    rgba(60, 40, 80, 0.6) 0%,
    rgba(30, 20, 50, 0.4) 50%,
    rgba(15, 15, 30, 0.2) 90%
  );
  z-index: 1;
  border-radius: 50%;
  transform: scale(2);
  opacity: 0.9;
  filter: blur(8px);
}

/* Base style for background images */
.sweet-dynamic-bg > img.bg-image {
  position: absolute;
  width: 200%;
  height: 200%;
  border-radius: 100em;
  opacity: 0; /* Start hidden */
  transition: opacity 1s ease-in-out; /* Transition for crossfade */
  will-change: transform, opacity;
  transform-style: flat;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* Primary background image styling */
.sweet-dynamic-bg > img.primary {
  position: absolute;
  right: 0;
  top: 0;
  width: 200%;
  height: 200%;
  border-radius: 100em;
  z-index: 3;
  transform: rotate(var(--bg-rotation-primary, 0deg)) scale(var(--bg-scale-primary, 1));
  filter: blur(var(--bg-blur-primary)) hue-rotate(var(--bg-hue-shift)) brightness(1.1);
  animation: bgAnimPrimary 60s linear infinite;
  mix-blend-mode: overlay; /* Enhanced blending with background */
}

/* Secondary background image styling */
.sweet-dynamic-bg > img.secondary {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 200%;
  height: 200%;
  border-radius: 100em;
  z-index: 2;
  transform: rotate(var(--bg-rotation-secondary, 0deg)) scale(var(--bg-scale-secondary, 1));
  filter: blur(var(--bg-blur-secondary)) hue-rotate(calc(var(--bg-hue-shift) + 30deg)) brightness(1.2);
  animation: bgAnimSecondary 75s linear infinite reverse;
  mix-blend-mode: soft-light; /* Softer blending for secondary layer */
}

/* Active state for crossfade */
.sweet-dynamic-bg > img.primary.active {
  opacity: 1; /* Primary is fully opaque when active */
}

.sweet-dynamic-bg > img.secondary.active {
  opacity: 0.8; /* Secondary is slightly transparent when active */
}


/* Fullscreen mode optimizations */
#SpicyLyricsPage.Fullscreen .sweet-dynamic-bg {
  max-height: 60%;
  max-width: 20%;
  scale: 500% 170%; /* Keep existing scaling */
}

.sweet-dynamic-bg-in-this {
  overflow: hidden;
  position: relative;
}
/* Specific styling for sidebar */
.sweet-dynamic-bg-in-this:is(aside) .sweet-dynamic-bg {
  --bg-saturation: 2;
  --bg-brightness: 0.45;
  max-height: 100%;
  max-width: 100%;
}
/* Video element styling (Keep as is) */
.sweet-dynamic-bg-in-this:is(aside) video {
  filter: opacity(0.75) brightness(0.5);
  -webkit-mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 35%, rgba(0, 0, 0, 0) 90%);
  mask-image: linear-gradient(to bottom, rgba(0, 0, 0, 1) 35%, rgba(0, 0, 0, 0) 90%);
}
/* Remove default styling from video containers (Keep as is) */
.main-nowPlayingView-coverArtContainer div:has(video)::before,
.main-nowPlayingView-coverArtContainer div:has(video)::after {
  display: none;
}
/* Ensure content stays above background (Keep as is) */
.sweet-dynamic-bg-in-this:is(aside) .AAdBM1nhG73supMfnYX7 {
  z-index: 10;
  position: relative;
}
/* Lyrics page specific styling */
#SpicyLyricsPage .sweet-dynamic-bg {
  --bg-saturation: 2.5;
  --bg-brightness: 0.45;
  max-height: 55%;
  max-width: 35%;
  scale: 290% 185%;
  transform-origin: left top; /* Keep existing transform origin */
}

/* Optimized animation keyframes for primary element (Keep as is) */
@keyframes bgAnimPrimary {
  0% {
    transform: rotate(var(--bg-rotation-primary, 0deg)) scale(var(--bg-scale-primary, 1));
  }
  to {
    transform: rotate(calc(var(--bg-rotation-primary, 0deg) + 1turn)) scale(var(--bg-scale-primary, 1));
  }
}

/* Animation for gradient overlay movement */
@keyframes gradientShift {
  0% {
    background-position: 0% 0%;
    opacity: 0.7;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    background-position: 100% 100%;
    opacity: 0.7;
  }
}
/* Optimized animation keyframes for secondary element (Keep as is) */
@keyframes bgAnimSecondary {
  0% {
    transform: rotate(var(--bg-rotation-secondary, 0deg)) scale(var(--bg-scale-secondary, 1));
  }
  to {
    transform: rotate(calc(var(--bg-rotation-secondary, 0deg) + 1turn)) scale(var(--bg-scale-secondary, 1));
  }
}
/* Performance optimization for fullscreen mode (Keep as is) */
body:has(#SpicyLyricsPage.Fullscreen) .Root__right-sidebar aside:is(.NowPlayingView, .sweet-dynamic-bg-in-this) .sweet-dynamic-bg,
body:has(#SpicyLyricsPage.Fullscreen) .Root__right-sidebar aside:is(.NowPlayingView, .sweet-dynamic-bg-in-this) .sweet-dynamic-bg * {
  display: none !important;
  animation: none !important;
  filter: none !important;
}
/* Media query for lower-end devices */
@media (prefers-reduced-motion), (max-width: 768px) {
  .sweet-dynamic-bg {
    --bg-saturation: 1.2;
    --bg-brightness: 0.5;
  }

  /* Adjust animation/filter directly on the image elements */
  .sweet-dynamic-bg > img.primary {
    animation-duration: 120s; /* Slower animation */
    filter: blur(20px) hue-rotate(var(--bg-hue-shift)); /* Adjusted blur */
  }

  .sweet-dynamic-bg > img.secondary {
    animation-duration: 120s; /* Slower animation */
    filter: blur(20px) hue-rotate(calc(var(--bg-hue-shift) + 30deg)); /* Adjusted blur */
  }

  /* No need to disable canvas as it's removed */
}
