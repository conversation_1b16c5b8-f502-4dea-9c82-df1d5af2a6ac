export const PageViewSelectors = {
  PageRoot: '.Root__main-view .main-view-container div[data-overlayscrollbars-viewport]',
  SpicyLyricsPage: '#SpicyLyricsPage',
  ContentBox: '#SpicyLyricsPage .ContentBox',
  MediaImage: '#SpicyLyricsPage .MediaImage',
  SongName: '#SpicyLyricsPage .SongName span',
  Artists: '#SpicyLyricsPage .Artists span',
  ViewControls: '#SpicyLyricsPage .ContentBox .ViewControls',
  Header: '#SpicyLyricsPage .ContentBox .NowBar .Header',
  HeaderViewControls: '#SpicyLyricsPage .ContentBox .NowBar .Header .ViewControls',
  RefreshLyricsButton: '#RefreshLyrics',
  WatchMusicVideoButton: '#WatchMusicVideoButton',
  ReleaseLogsButton: '#ReleaseLogsButton',
  CloseButton: '#Close',
  FullscreenToggleButton: '#FullscreenToggle',
  LoaderContainer: '#SpicyLyricsPage .loaderContainer',
  LyricsContent: '#SpicyLyricsPage .LyricsContent',
  NowBar: '#SpicyLyricsPage .NowBar',
  NotificationContainer: '#SpicyLyricsPage .NotificationContainer',
};